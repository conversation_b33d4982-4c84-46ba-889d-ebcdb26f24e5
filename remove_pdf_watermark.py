import pikepdf
import argparse
import os

def main():
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='Remove watermark text from PDF files')
    parser.add_argument('input_pdf', help='Input PDF file path')
    parser.add_argument('watermark_text', help='Watermark text to remove')
    parser.add_argument('-o', '--output', help='Output PDF file path (default: adds "_cleaned" suffix to input filename)')

    args = parser.parse_args()

    input_pdf = args.input_pdf
    watermark_text = args.watermark_text

    # 如果没有指定输出文件名，则自动生成
    if args.output:
        output_pdf = args.output
    else:
        # 从输入文件名生成输出文件名
        base_name = os.path.splitext(input_pdf)[0]
        extension = os.path.splitext(input_pdf)[1]
        output_pdf = f"{base_name}_cleaned{extension}"

    # 检查输入文件是否存在
    if not os.path.exists(input_pdf):
        print(f"错误：输入文件 '{input_pdf}' 不存在")
        return

    try:
        # 打开 PDF
        pdf = pikepdf.open(input_pdf)

        # 遍历所有页面对象
        for page in pdf.pages:
            # 解析页面内容流
            content = page.Contents
            if content is not None:
                # 检查 Contents 是否为数组
                if isinstance(content, list):
                    # 如果是数组，处理每个流对象
                    for stream_obj in content:
                        if hasattr(stream_obj, 'read_bytes'):
                            stream = stream_obj.read_bytes().decode("latin-1")
                            # 替换掉水印文字
                            stream = stream.replace(watermark_text, "")
                            # 写回页面
                            stream_obj.write(stream.encode("latin-1"))
                else:
                    # 如果是单个流对象
                    if hasattr(content, 'read_bytes'):
                        stream = content.read_bytes().decode("latin-1")
                        # 替换掉水印文字
                        stream = stream.replace(watermark_text, "")
                        # 写回页面
                        content.write(stream.encode("latin-1"))

        # 保存新文件
        pdf.save(output_pdf)
        pdf.close()

        print(f"已生成无水印版本：{output_pdf}")

    except Exception as e:
        print(f"处理PDF时发生错误：{e}")

if __name__ == "__main__":
    main()